//
//  MusicListTableViewCell.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/14.
//
import UIKit
import Foundation

// Basic TableViewCell for Music List - Customize as needed
class MusicListTableViewCell: UITableViewCell {
    static let identifier = "MusicListTableViewCell"

    let artworkImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.layer.cornerRadius = 4
        iv.backgroundColor = .lightGray // Placeholder color
        return iv
    }()

    let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(hex: "#FF8F1F")
        label.lineBreakMode = .byTruncatingTail // 设置文本截断模式
        label.numberOfLines = 1 // 限制为单行
        return label
    }()

    let artistLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13)
        label.textColor = UIColor(hex: "#000000", alpha: 0.45)
        return label
    }()

    let durationLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13)
        label.textColor = UIColor(hex: "#000000", alpha: 0.45)
        return label
    }()

    let playingIndicatorImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFit
        iv.image = UIImage(named: "video_edit_music_playing") // Example: speaker icon
        iv.tintColor = UIColor(hex: "#FF8F1F") // Orange color
        iv.isHidden = true
        return iv
    }()

    let favoriteButton: UIButton = {
        let button = UIButton(type: .custom)
//        button.setImage(UIImage(systemName: "star"), for: .normal)
//        button.setImage(UIImage(systemName: "star.fill"), for: .selected)
        button.setImage(UIImage(named: "video_edit_music_collect"), for: .selected)
        button.setImage(UIImage(named: "video_edit_music_notcollect"), for: .normal)
        button.tintColor = UIColor(hex: "#FF8F1F")
        return button
    }()
    
    let downloadButton: UIButton = {
        let button = UIButton(type: .custom)
//        button.setImage(UIImage(systemName: "arrow.down.circle"), for: .normal) // Download icon
        button.setImage(UIImage(named: "video_download_btn"), for: .normal)
        button.tintColor = UIColor(hex: "#FF8F1F")
        return button
    }()

    let downloadActivityIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = UIColor(hex: "#FF8F1F") // 明显的橙色
        indicator.hidesWhenStopped = true
        return indicator
    }()

    let downloadProgressLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 10, weight: .medium)
        label.textColor = UIColor(hex: "#FF8F1F")
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()
    
    var favoriteButtonAction: (() -> Void)?
    var downloadButtonAction: (() -> Void)?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        contentView.backgroundColor = .clear
        backgroundColor = .clear

        contentView.addSubview(artworkImageView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(playingIndicatorImageView)
        contentView.addSubview(artistLabel)
        contentView.addSubview(durationLabel)
        contentView.addSubview(favoriteButton)
        contentView.addSubview(downloadButton)
        contentView.addSubview(downloadActivityIndicator)
        contentView.addSubview(downloadProgressLabel)

        favoriteButton.addTarget(self, action: #selector(didTapFavorite), for: .touchUpInside)
        downloadButton.addTarget(self, action: #selector(didTapDownload), for: .touchUpInside)

        artworkImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(46)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(artworkImageView.snp.right).offset(12)
            make.top.equalTo(artworkImageView.snp.top)
            make.height.equalTo(23)
            make.right.lessThanOrEqualTo(favoriteButton.snp.left).offset(-8) // 添加右侧约束，防止文本超长
        }
        
        playingIndicatorImageView.snp.makeConstraints { make in
            make.left.equalTo(titleLabel.snp.right).offset(8)
            make.centerY.equalTo(titleLabel)
            make.width.height.equalTo(16)
        }

        artistLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(3)
        }

        durationLabel.snp.makeConstraints { make in
            make.left.equalTo(artistLabel.snp.right).offset(8)
            make.centerY.equalTo(artistLabel)
        }

        favoriteButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-10) // Favorite left of download
            make.centerY.equalToSuperview()
            make.width.height.equalTo(28)
        }
        
        downloadButton.snp.makeConstraints { make in
            make.center.equalTo(artworkImageView)
            make.height.width.equalTo(30)
        }

        downloadActivityIndicator.snp.makeConstraints { make in
            make.center.equalTo(artworkImageView)
            make.width.height.equalTo(24)
        }

        downloadProgressLabel.snp.makeConstraints { make in
            make.centerX.equalTo(artworkImageView)
            make.top.equalTo(downloadActivityIndicator.snp.bottom).offset(2)
            make.width.equalTo(artworkImageView)
        }
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc private func didTapFavorite() {
        favoriteButtonAction?()
    }
    
    @objc private func didTapDownload() {
        downloadButtonAction?()
    }

    func configure(with item: MusicItem) {
        titleLabel.text = item.title
        artistLabel.text = item.artist
        durationLabel.text = item.duration
        // 设置图片，支持默认图标
        if let artworkURLString = item.artworkURLString, !artworkURLString.isEmpty {
            artworkImageView.kf.setImage(
                with: URL(string: artworkURLString),
                placeholder: UIImage(named: "login_logo"), // 使用login_logo作为占位图
                options: [.transition(.fade(0.2))]
            ) { [weak self] result in
                // 如果加载失败，使用默认图标
                switch result {
                case .failure:
                    self?.artworkImageView.image = UIImage(named: "login_logo")
                case .success:
                    break
                }
            }
        } else {
            // API没有提供图标时，使用本地login_logo资源
            artworkImageView.image = UIImage(named: "login_logo")
        }
        playingIndicatorImageView.isHidden = !item.isPlaying
        favoriteButton.isSelected = item.isFavorite
        
        // Apply border when item is playing
        if item.isPlaying {
            artworkImageView.layer.borderWidth = 2
            artworkImageView.layer.borderColor = UIColor(hex: "#FF8F1F").cgColor
        } else {
            artworkImageView.layer.borderWidth = 0
            artworkImageView.layer.borderColor = UIColor.clear.cgColor
        }

        switch item.downloadState {
        case .notDownloaded:
            downloadButton.isHidden = false
            downloadButton.setImage(UIImage(named: "video_download_btn"), for: .normal)
            downloadActivityIndicator.stopAnimating()
            downloadProgressLabel.isHidden = true
            downloadButton.isEnabled = true
        case .downloading(let progress):
            downloadButton.isHidden = true // 隐藏下载按钮
            downloadActivityIndicator.startAnimating() // 显示转菊花
            downloadProgressLabel.isHidden = true//暂时隐藏
            downloadProgressLabel.text = "\(Int(progress * 100))%"
            downloadButton.isEnabled = false
        case .downloaded:
            downloadButton.isHidden = true
            downloadActivityIndicator.stopAnimating()
            downloadProgressLabel.isHidden = true
        case .failed:
            downloadButton.isHidden = false
            downloadButton.setImage(UIImage(systemName: "exclamationmark.arrow.triangle.2.circlepath"), for: .normal) // Retry icon
            downloadActivityIndicator.stopAnimating()
            downloadProgressLabel.isHidden = true
            downloadButton.isEnabled = true
        }
    }
}
